# Knock MCP Server

A Python-based server application for the Knock MCP (Model Context Protocol) system.

## Prerequisites

- Python 3.11 or higher
- uv (Python package installer and runner)
- Docker (optional, for containerized deployment)

## Virtual Environment Setup

### Option 1: Using uv (Recommended)

uv automatically manages virtual environments for you. When you run `uv sync`, it will create and manage a virtual environment automatically.

else, use the below command to setup a local venv
    uv venv .venv

### Option 2: Using Python venv

If you prefer to use Python's built-in virtual environment:

1. Create a virtual environment:
```bash
python -m venv .venv
```

2. Activate the virtual environment:
   - On macOS/Linux:
   ```bash
   source YOUR_VENV/bin/activate
   ```
   - On Windows:
   ```bash
   YOUR_VENV\Scripts\activate
   ```

3. Deactivate when done:
```bash
deactivate
```

## Installation

1. Clone the repository:
```bash
<NAME_EMAIL>:RealPage/mcp-knock.git
cd mcp-knock
```

2. Install uv (if not already installed):
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

3. Install dependencies using uv:
```bash
uv sync
```

## Development Setup

1. Install development dependencies:
```bash
uv sync --all-extras --dev
```

2. Install pre-commit hooks:
```bash
uv run pre-commit install
```

## Running the Application

1. Start the development server using uv:
```bash
uv run uvicorn main:app --reload --port 8000
```

2. Alternatively, you can start the application using uv with the main module:
```bash
uv run python main.py
```

3. For production deployment, you can use:
```bash
uv run uvicorn main:app --host 0.0.0.0 --port 8000
```

## Project Structure

```
knock-mcp-server/
├── src/               # Source code
│       ├── __init__.py
│       └── main.py
├── tests/            # Test files
├── pyproject.toml    # Project configuration and dependencies
└── README.md         # This file
```

## Development

- Run tests:
```bash
uv run pytest
```

- Run linting:
```bash
uv run ruff check .
```

- Format code:
```bash
uv run ruff format .
```

- Run type checking:
```bash
uv run mypy src/
```

- Run pre-commit hooks:
```bash
uv run pre-commit run --all-files
```

## OpenTelemetry Configuration

The application supports OpenTelemetry for distributed tracing, metrics, and logging. To enable OpenTelemetry:

1. Set the `OTEL_ENABLED` environment variable to `true`:
```bash
export OTEL_ENABLED=true
```

2. Configure the OpenTelemetry collector endpoint (optional, defaults to `http://localhost:4318`):
```bash
export OTEL_ENDPOINT=http://your-collector:4318
```

3. If no OpenTelemetry collector is running, the application will continue to work normally without telemetry.

### Running with OpenTelemetry Collector

To run a local OpenTelemetry collector for development:

```bash
# Using Docker
docker run -p 4318:4318 -p 4317:4317 otel/opentelemetry-collector:latest
```

## Docker Support

To build and run the Docker container:

```bash
docker build -t knock-mcp-server .
docker run -p 8000:8000 knock-mcp-server
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

[Add your license information here]

## Contact

[Add your contact information here]


## Contribution Checklist

- [x] Clean, simple, well styled code
- [x] Commits should be atomic and messages must be descriptive. Related issues should be mentioned by Issue number.
- [x] Comments
  - Module-level & function-level comments.
  - Comments on complex blocks of code or algorithms (include references to sources).
- [x] Tests
  - The test suite, if provided, must be complete and pass
  - Increase code coverage, not versa.
- [x] Dependencies
  - Minimize number of dependencies.
  - Prefer Apache 2.0, BSD3, MIT, ISC and MPL licenses.
- [x] Reviews
  - Changes must be approved via peer code review
