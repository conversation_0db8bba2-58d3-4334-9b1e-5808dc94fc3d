"""
Pydantic models for admin property info response.
"""


from pydantic import BaseModel, Field

from src.models.knock_non_admin_property_info import (
    Amenity,
    Property,
    PropertyData,
)


class CustomFee(BaseModel):
    amount: float
    name: str
    property_id: int


class AffordableHouseProgram(BaseModel):
    available: bool
    name: str


class AffordableHousing(BaseModel):
    notes: str
    programs: list[AffordableHouseProgram]


class AmenityTypes(BaseModel):
    additional: list[Amenity]
    exterior: list[Amenity]
    heatingAndCooling: list[Amenity]
    recreation: list[Amenity]
    security: list[Amenity]
    wiring: list[Amenity]


class Costable(BaseModel):
    isCovered: bool
    name: str


class Description(BaseModel):
    short: str


class FeaturedAmenityType(BaseModel):
    types: list[Amenity]


class FeaturedAmenities(BaseModel):
    gym: FeaturedAmenityType
    laundry: FeaturedAmenityType
    parking: FeaturedAmenityType
    pets: FeaturedAmenityType
    pool: FeaturedAmenityType


class Website(BaseModel):
    primaryColor: str


class Video(BaseModel):
    thumb_url: str
    url: str


class UtilityType(BaseModel):
    included: bool
    name: str


class Utilities(BaseModel):
    estimatedCost: str
    types: list[UtilityType]


class AdminPropertyData(PropertyData):
    affordableHousing: AffordableHousing
    amenities: AmenityTypes
    appliances: list[Amenity]
    costables: list[Costable]
    created_time: str
    debug: dict = Field(default_factory=dict)
    description: Description
    editorType: str
    extra: dict = Field(default_factory=dict)
    featured_amenities: FeaturedAmenities
    furnishing: list[Amenity]
    updated_time: str
    utilities: Utilities
    videos: list[Video]
    views: list[Amenity]
    website: Website


class BusinessHours(BaseModel):
    end_time: str
    is_active: bool
    start_time: str


class MessageTemplates(BaseModel):
    away: str
    away_response: str
    greeting: str
    guest: str


class SnippetConfig(BaseModel):
    community_id: str
    host: str
    token: str


class PreferencesSettings(BaseModel):
    button_color: str
    button_greeting: str
    display_launcher_button: bool
    emit_analytics_events: bool
    emit_tag_manager_events: bool
    messaging_enabled: bool
    primary_color: str
    script_major_version: str


class Settings(BaseModel):
    hours: dict[str, BusinessHours]
    message_templates: MessageTemplates
    preferences: PreferencesSettings
    snippet: str
    snippetConfig: SnippetConfig


class PropertyPreferences(BaseModel):
    created_time: str
    disable_pricing_availability: bool
    enable_affordable_housing: bool
    enable_selfie_scan: bool
    id: int
    is_deleted: bool
    modified_time: str
    preferences: dict
    property_id: int
    require_lease_term: bool
    require_preferred_bedroom_count: bool
    require_preferred_unit: bool
    require_prospect_floorplan: bool
    require_prospect_move_in_date: bool
    require_unit_scheduling: bool
    suppressed_autoresponders: list[str]
    suppressed_outbound_channels: list[str]


class AdminProperty(Property):
    custom_fees: list[CustomFee]
    preferences: PropertyPreferences
    data: AdminPropertyData


class AdminPropertyResponse(BaseModel):
    property: AdminProperty
    settings: Settings
    status_code: str
