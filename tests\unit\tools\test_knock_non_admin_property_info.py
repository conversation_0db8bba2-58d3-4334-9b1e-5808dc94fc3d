from datetime import datetime
from unittest.mock import MagicMock

from fastapi.testclient import Test<PERSON>lient

from main import app
from src.models.knock_non_admin_property_info import (
    Address,
    Application,
    Doorway,
    GeoLocation,
    KeySellingPoints,
    LeaseLength,
    Leasing,
    LeasingTerms,
    Location,
    PetPolicy,
    Pets,
    Property,
    PropertyData,
    PropertyResponse,
    QuickLaunchMenuItem,
    Social,
)

client = TestClient(app)


def test_get_property_info_endpoint() -> None:
    """Test the GET /propertyinfo/{property_id} endpoint"""
    property_id = 123
    response = client.get(f"/propertyinfo/non-admin/{property_id}")

    assert response.status_code == 200
    data = response.json()

    # Verify response structure
    assert "property" in data
    assert "status_code" in data
    assert data["status_code"] == "ok"

    # Verify property data
    property_data = data["property"]
    assert property_data["id"] == property_id
    assert "created_time" in property_data
    assert datetime.fromisoformat(property_data["created_time"])  # Validate datetime format
    assert "modified_time" in property_data
    assert datetime.fromisoformat(property_data["modified_time"])  # Validate datetime format
    assert "data" in property_data

    # Verify nested property data
    nested_data = property_data["data"]
    assert nested_data["property_id"] == property_id
    assert "doorway" in nested_data
    assert "key_selling_points" in nested_data
    assert "location" in nested_data
    assert "pets" in nested_data

    # Test doorway configuration
    doorway = nested_data["doorway"]
    assert doorway["applyNowIsActive"] is True
    assert doorway["availabilityIsActive"] is True
    assert doorway["bannerText"] == "Welcome to our community!"
    assert doorway["galleryIsActive"] is True
    assert len(doorway["includeInQuickLaunchMenuList"]) == 2
    assert doorway["includeInQuickLaunchMenuList"][0]["name"] == "schedule"
    assert doorway["includeInQuickLaunchMenuList"][1]["name"] == "gallery"
    assert doorway["residentPortalURL"] == "https://example.com/portal"

    # Test key selling points
    selling_points = nested_data["key_selling_points"]
    assert "Great amenities" in selling_points["community"]
    assert "24/7 maintenance" in selling_points["community"]
    assert "Close to shopping" in selling_points["location"]
    assert "Easy highway access" in selling_points["location"]
    assert "Modern appliances" in selling_points["units"]
    assert "Spacious floor plans" in selling_points["units"]

    # Test leasing information
    leasing = nested_data["leasing"]
    assert leasing["application"]["fee"] == "$50"
    assert leasing["application"]["instructions"] == "Apply online"
    assert leasing["provider"] == "Mock Provider"
    assert leasing["terms"]["breakPenalty"] == "Two months rent"
    assert leasing["terms"]["deposit"] == "$500"
    assert len(leasing["terms"]["leaseLengths"]) == 1
    assert leasing["terms"]["leaseLengths"][0]["leaseLength"] == "12"
    assert leasing["terms"]["leaseLengths"][0]["lengthUnit"] == "months"
    assert leasing["terms"]["leasingSpecial"] == "First month free"

    # Test location information
    location = nested_data["location"]
    assert location["address"]["city"] == "Sample City"
    assert location["address"]["street"] == "123 Main St"
    assert location["address"]["state"] == "CA"
    assert location["address"]["zip"] == "12345"
    assert location["geo"]["coordinates"] == [-122.419416, 37.774929]
    assert location["geo"]["type"] == "point"
    assert location["name"] == f"Property {property_id}"
    assert location["numberOfUnits"] == 100
    assert location["yearBuilt"] == 2020

    # Test pet information
    pets = nested_data["pets"]
    assert pets["allowed"]["cats"] is True
    assert pets["allowed"]["large_dogs"] is False
    assert pets["allowed"]["small_dogs"] is True
    assert pets["deposit"] == "$300"
    assert pets["fee"] == "$50"
    assert pets["rent"] == "$30"
    assert pets["notes"] == "Weight limit 25 lbs"

    # Test social information
    social = nested_data["social"]
    assert social["knock_email"] == "<EMAIL>"
    assert social["shortlink"] == "https://example.com/p/123"
    assert social["website"] == "https://example.com"

    # Test property metadata
    assert property_data["leasing_team_id"] == 1000
    assert property_data["owning_manager_id"] == 2000
    assert property_data["public_id"] == f"prop_{property_id}"
    assert property_data["resource_id"] == 3000
    assert property_data["timezone"] == "America/Los_Angeles"
    assert property_data["type"] == "multi-family"


def test_property_response_model() -> None:
    """Test the PropertyResponse Pydantic model"""
    # Create a minimal valid property response
    property_response = PropertyResponse(
        property=Property(
            created_time=datetime.now().isoformat(),
            data=PropertyData(
                doorway=Doorway(
                    agentGuidedTourDisclaimerText="",
                    applyNowIsActive=True,
                    availabilityIsActive=True,
                    bannerText="Test Banner",
                    customAppointmentMessage="",
                    dynamicNumberInsertionType="",
                    excludeAppointmentMessagePhone=False,
                    faqsIsActive=True,
                    formattedNumber="",
                    formattedNumberIsActive=False,
                    galleryIsActive=True,
                    hideKnockBranding=False,
                    hidePricing=False,
                    hideUnitPreferences=False,
                    hideUnitSelector=False,
                    includeInQuickLaunchMenuList=[
                        QuickLaunchMenuItem(enabled=True, name="schedule"),
                        QuickLaunchMenuItem(enabled=True, name="gallery"),
                    ],
                    leasingInfoIsActive=True,
                    leasingSpecialIsActive=False,
                    limitAvailableUnitsShown=False,
                    liveVideoTourDisclaimerText="",
                    neighborhoodIsActive=True,
                    petInfoIsActive=True,
                    renterPortalIsActive=True,
                    requestTextIsActive=True,
                    residentPortalURL="https://example.com",
                    scheduleATourContentBox="",
                    selfGuidedTourDisclaimerText="",
                    showNoteInput=False,
                    somethingElseIsActive=True,
                    useCustomWebsite=False,
                    useDynamicFloorplans=True,
                ),
                id="test_id",
                key_selling_points=KeySellingPoints(
                    community=["Test community point"],
                    location=["Test location point"],
                    units=["Test unit point"],
                ),
                leasing=Leasing(
                    application=Application(
                        fee="$50",
                        instructions="Test",
                        isDefaultApplicationUrlOverridden=False,
                        link="",
                    ),
                    provider="Test Provider",
                    qualificationCriteria="Test criteria",
                    terms=LeasingTerms(
                        breakPenalty="Test",
                        deposit="$500",
                        includeUpcharges=False,
                        leaseLengths=[LeaseLength(isAvailable=True, leaseLength="12", lengthUnit="months")],
                        leasingSpecial="Test special",
                        notes="",
                    ),
                ),
                location=Location(
                    address=Address(
                        city="Test City",
                        house="",
                        neighborhood="Test Area",
                        raw="123 Test St",
                        state="CA",
                        street="123 Test St",
                        zip="12345",
                    ),
                    geo=GeoLocation(coordinates=[-122.0, 37.0], type="point"),
                    name="Test Property",
                    needs_review=False,
                    numberOfUnits=100,
                    timezone="America/Los_Angeles",
                    yearBuilt=2020,
                ),
                logos=[],
                notes="",
                pets=Pets(
                    allowed=PetPolicy(cats=True, large_dogs=False, none=False, small_dogs=True),
                    deposit="$300",
                    fee="$50",
                    notes="Test pet notes",
                    rent="$30",
                ),
                property_id=123,
                social=Social(
                    facebook="",
                    knock_email="<EMAIL>",
                    shortlink="https://example.com/short",
                    website="https://example.com",
                ),
            ),
            id=123,
            is_deleted=False,
            leasing_team_id=1000,
            modified_time=datetime.now().isoformat(),
            owning_manager_id=2000,
            public_id="test_public_id",
            resource_id=3000,
            timezone="America/Los_Angeles",
            type="multi-family",
        ),
        status_code="ok",
    )

    assert property_response.property.id == 123
    assert property_response.status_code == "ok"
    assert property_response.property.data.pets.allowed.cats is True
    assert property_response.property.data.pets.allowed.large_dogs is False

    # Test QuickLaunchMenuItem
    menu_items = property_response.property.data.doorway.includeInQuickLaunchMenuList
    assert len(menu_items) == 2
    assert menu_items[0].enabled is True
    assert menu_items[0].name == "schedule"
    assert menu_items[1].name == "gallery"

    # Test LeaseLength
    lease_lengths = property_response.property.data.leasing.terms.leaseLengths
    assert len(lease_lengths) == 1
    assert lease_lengths[0].isAvailable is True
    assert lease_lengths[0].leaseLength == "12"
    assert lease_lengths[0].lengthUnit == "months"


def test_invalid_property_id() -> None:
    """Test the endpoint with invalid property IDs"""
    # Test with string ID
    response = client.get("/propertyinfo/non-admin/invalid_id")
    assert response.status_code == 422  # FastAPI validation error
    error_detail = response.json()["detail"][0]
    assert error_detail["msg"] == "Input should be a valid integer, unable to parse string as an integer"
    assert error_detail["type"] == "int_parsing"
    assert error_detail["input"] == "invalid_id"

    # Test with zero ID
    response = client.get("/propertyinfo/non-admin/0")
    assert response.status_code == 422
    assert "Property ID must be a positive integer" in response.json()["detail"]

    # Test with negative ID
    response = client.get("/propertyinfo/non-admin/-1")
    assert response.status_code == 422
    assert "Property ID must be a positive integer" in response.json()["detail"]


def test_nonexistent_property_id() -> None:
    """Test the endpoint with a property ID that doesn't exist"""
    response = client.get("/propertyinfo/non-admin/999999")
    assert response.status_code == 200  # Since we're using mock data, it should still return 200
    data = response.json()

    # Even non-existent IDs should return a valid response with that ID
    assert data["property"]["id"] == 999999
    assert data["status_code"] == "ok"

    # Verify the mock data is properly structured
    property_data = data["property"]["data"]
    assert property_data["property_id"] == 999999
    assert property_data["id"] == "999999"
    assert property_data["location"]["name"] == "Property 999999"
    assert datetime.fromisoformat(data["property"]["created_time"])
    assert datetime.fromisoformat(data["property"]["modified_time"])


def test_multiple_property_ids() -> None:
    """Test that the endpoint rejects requests with multiple property IDs"""
    # Test with comma-separated IDs
    response = client.get("/propertyinfo/non-admin/1,2,3")
    assert response.status_code == 422  # FastAPI validation error - invalid integer

    # Test with array-style IDs
    response = client.get("/propertyinfo/non-admin/[1,2,3]")
    assert response.status_code == 422  # FastAPI validation error - invalid integer


def test_non_admin_mcp_tool_registration() -> None:
    """Test MCP tool registration"""
    from src.tools.property_info import register_property_info_tools

    # Create a mock MCP instance
    mock_mcp = MagicMock()
    mock_tool_decorator = MagicMock()
    mock_mcp.tool = mock_tool_decorator

    # Register the tools
    register_property_info_tools(mock_mcp)

    # Verify tool registration
    assert mock_tool_decorator.call_count == 3  # Should be called twice

    # Get the calls
    calls = mock_tool_decorator.call_args_list

    # Verify non-admin tool registration (first call)
    non_admin_call = calls[0]
    assert non_admin_call.kwargs["name"] == "get_knock_non_admin_property_info"
    assert "Get the property info of non admin knock properties" in non_admin_call.kwargs["description"]


def test_non_admin_async_endpoints() -> None:
    """Test the async FastAPI endpoint functions"""
    import asyncio

    from src.tools.property_info import knock_non_admin_property_info

    # Test non-admin endpoint
    property_id = 123
    response = asyncio.run(knock_non_admin_property_info(property_id))
    assert response.property.id == property_id
    assert response.status_code == "ok"
    assert response.property.data.property_id == property_id
    assert response.property.data.location.name == f"Property {property_id}"
