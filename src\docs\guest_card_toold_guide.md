# Guest Card Tools Guide

This document provides a comprehensive guide to the guest card management tools available in the Knock MCP Server.

## Overview

The guest card tools provide a complete set of endpoints for managing prospect guest cards. These endpoints are automatically converted to MCP tools by the FastApiMCP framework, making them available for use in AI-powered workflows.

## Available Tools

### 1. Update Prospect Guest Card
- **Endpoint**: `PUT /guestcard/{prospect_id}`
- **Description**: Updates details of a prospect guest card
- **Parameters**:
  - `prospect_id` (path): Unique identifier of the prospect guest card

## Usage Examples

### Updating a Prospect Guest Card (Profile & Preferences)
```python
import requests

prospect_id = 122123
update_data = {
    "profile": {
        "first_name": "<PERSON>",
        "last_name": "<PERSON>",
        "target_move_date": "2024-07-01",
        "email": "<EMAIL>",
        "phone": {"phone_number": "******-456-7890"}
    },
    "preferences": {
        "bedrooms": ["2", "3"]
    }
}

response = requests.put(
    f"http://localhost:8000/guestcard/{prospect_id}",
    json=update_data
)
updated_card = response.json()
print(f"Updated card profile: {updated_card['profile']}")
print(f"Updated preferences: {updated_card['preferences']}")
```

This example demonstrates how to update both the guest's profile and their preferences (such as bedroom count) using the `PUT /guestcard/{prospect_id}` endpoint. Only the provided fields will be updated. The request and response models are defined in the `guest_card_tool.py` tool as `GuestCardProfile` and `GuestCardPreferences`.

## Mock Implementation Notes

The current implementation uses mock data for demonstration purposes. In a production environment, you would need to:

1. Replace mock data with actual database operations
2. Add proper authentication and authorization
3. Implement data validation and sanitization
4. Add audit logging for all operations
5. Implement proper error handling and status codes
6. Add rate limiting and security measures

## Integration with MCP

These endpoints are automatically converted to MCP tools by the FastApiMCP framework. This means they can be used by AI assistants and other MCP-compatible clients to manage guest cards programmatically.

The tools will be available with descriptive names and parameter documentation, making them easy to use in AI-powered workflows for facility management.
