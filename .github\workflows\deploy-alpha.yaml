---
name: Deploy Alpha

on:
  push:
    branches: [main]
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

jobs:
  build-and-push-image:
    uses: knockrentals/reusable-workflows/.github/workflows/build-and-push-image.yaml@v0.0.7
    with:
      upload-role: alpha-mcp-knock-gh-upload
      ecr-name: mcp-knock
      docker-build-context: .
      dockerfile-path: .
  deploy-image:
    needs: [build-and-push-image]
    uses: knockrentals/reusable-workflows/.github/workflows/deploy-image.yaml@v0.0.7
    with:
      config-path: infra/alpha
      deploy-role: alpha-mcp-knock-gh-deploy
      target-account: ************
      tag: ${{ needs.build-and-push-image.outputs.tag }}
