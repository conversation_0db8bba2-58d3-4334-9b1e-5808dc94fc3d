from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Any

from pydantic import BaseModel, HttpUrl


class GuestCardPhone(BaseModel):
    phone_number: str | None = None


class GuestCardProfile(BaseModel):
    first_name: str
    last_name: str
    target_move_date: str | None = None  # YYYY-MM-DD
    email: str | None = None
    phone: GuestCardPhone | None = None


class GuestCardPreferences(BaseModel):
    bedrooms: list[str] | None = None


class GuestCardRequestType(BaseModel):
    """
    Request type for updating a guest card.
    """

    profile: GuestCardProfile
    preferences: GuestCardPreferences | None = None


class ProspectStatusResponse(BaseModel):
    """
    Response type for updating prospect status.
    """

    prospect_id: int
    status: str


class ProspectActivityResponse(BaseModel):
    """
    Response type for adding activity to a prospect.
    """

    prospect_id: int
    activity: str


class AiEmail(BaseModel):
    enabled: bool
    togglable: bool
    tooltip: str


class Community(BaseModel):
    id: str


class SmsConsent(BaseModel):
    bypass_consent: bool
    created_time: datetime
    has_consent: bool
    has_express_consent_override: bool
    id: int
    is_deleted: bool
    modified_time: datetime
    note: str | None = None
    status: str


class Event(BaseModel):
    appointment_property_units: list[Any]
    auto_accepted: bool
    created_time: datetime
    developer_id: str | None = None
    end_time: datetime
    event_time: datetime
    event_type: str
    id: int
    is_deleted: bool
    manager_id: int
    modified_time: datetime
    origin: str
    prospect_id: int
    reference_info: Any | None = None
    reminder_id: str
    request_id: int | None = None
    resource_id: int
    review_reminder_id: str | None = None
    review_token: str | None = None
    shown_units: list[Any]
    sms_consent: SmsConsent
    source: str
    start_time: datetime
    status: str
    tour_type: str | None = None
    type: str
    uuid: str


class Preferences(BaseModel):
    amenities: Any | None = None
    bedrooms: list[Any]
    created_by_type: str
    created_time: datetime
    deal_breakers: Any | None = None
    id: int
    is_deleted: bool
    max_price: int
    min_price: int
    modified_time: datetime
    must_have: Any | None = None
    neighborhoods: Any | None = None
    number_of_occupants: Any | None = None
    preferred_layout_id: Any | None = None
    preferred_lease_term_months: int
    preferred_property_floorplan_id: Any | None = None
    preferred_property_unit_id: Any | None = None
    preferred_unit_id: Any | None = None
    preferrred_property_floorplan: Any | None = None
    preferrred_property_unit: Any | None = None


class Phone(BaseModel):
    caller_id_name: str
    caller_id_type: Any | None = None
    can_receive_call: bool
    can_receive_sms: bool
    carrier_name: str
    carrier_type: str
    country_code: str
    created_time: datetime
    id: int
    is_deleted: bool
    modified_time: Any | None = None
    national_format: str
    phone_number: str


class Profile(BaseModel):
    address_id: Any | None = None
    bio: Any | None = None
    co_tenants: Any | None = None
    created_by_type: str
    created_time: datetime
    email: Any | None = None
    first_name: str
    formatted_phone_number: str
    id: int
    id_verified: Any | None = None
    id_verify_report_url: Any | None = None
    income: Any | None = None
    is_criminal: Any | None = None
    is_deleted: bool
    is_winback_enabled: bool
    last_name: str
    modified_time: datetime
    pets: Any | None = None
    phone: Phone
    phone_id: int
    phone_number: str
    photo: HttpUrl
    selfiescan_pdf_key: Any | None = None
    target_move_date: datetime
    verification_method: Any | None = None
    was_evicted: Any | None = None


class Property(BaseModel):
    created_time: datetime
    id: int
    is_deleted: bool
    leasing_team_id: int
    modified_time: datetime
    owning_manager_id: int
    public_id: str
    resource_id: int
    timezone: str
    type: str


class TodoStatus(BaseModel):
    color: str
    explanation: str
    liveness: str
    urgency: float


class Prospect(BaseModel):
    ai_email: AiEmail
    assigned_manager_id: int
    assigned_relay_phone: str
    business_time_to_first_response: timedelta
    community: Community
    created_time: datetime
    creation_source: str
    developer_id: Any | None = None
    disable_follow_ups: bool
    disable_is_excluded: bool
    disable_lost_status: bool
    enable_cheatproof_engagement_score: bool
    events: list[Event]
    export_failures: list[Any]
    first_contact_response_time: float
    first_contact_type: str
    first_response_time: datetime
    follow_up_count: int
    has_appointments: bool
    has_call_recording: bool
    has_facebook_messenger: bool
    has_note: bool
    id: int
    integrations: list[Any]
    is_active: bool
    is_deleted: bool
    is_excluded: bool
    is_waitlist: bool
    is_winback_suppressed: bool
    last_contacted_time: datetime
    last_relevant_time: datetime
    last_response_time: datetime
    leased_date: Any | None = None
    loss_reasons: dict | None = None
    modified_time: datetime
    origin: str
    pms_created_time: Any | None = None
    preferences: Preferences
    preferences_id: int
    profile: Profile
    profile_id: int
    property: Property
    property_: Property | None = None
    property_id: int
    renter_id: int
    resource_id: int
    response_time_office_hours: float
    sms_consent: SmsConsent
    sms_consent_id: int
    source: str
    status: str
    stored_custom_fields: dict | None = None
    stream_id: str
    time_to_first_response: timedelta
    todo_status: TodoStatus | None = None
    existing_tour: Any | None = None
