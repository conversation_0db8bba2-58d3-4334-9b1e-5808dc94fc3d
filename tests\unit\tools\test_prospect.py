from fastapi.testclient import TestClient

from main import app
from src.tools.prospect import (
    GuestCardPreferences,
    GuestCardProfile,
    GuestCardRequestType,
    ProspectActivityResponse,
    ProspectStatusResponse,
    update_prospect_guestcard,
)

client = TestClient(app)


class TestGuestCardMCPTools:
    """Test cases for guest card MCP tools."""

    def test_update_prospect_guestcard_function(self) -> None:
        """Test the update_prospect_guestcard function directly."""
        prospect_id = 122123
        profile = GuestCardProfile(
            first_name="<PERSON>",
            last_name="<PERSON>",
            target_move_date="2024-07-01",
            email="<EMAIL>",
            phone={"phone_number": "******-456-7890"},
        )
        preferences = GuestCardPreferences(bedrooms=["2", "3"])

        result = update_prospect_guestcard(prospect_id, profile, preferences)

        assert result.profile.first_name == "Alice"
        assert result.profile.last_name == "<PERSON>"
        assert result.profile.email == "<EMAIL>"
        assert result.profile.phone.phone_number == "******-456-7890"
        assert result.preferences.bedrooms == ["2", "3"]

    def test_update_prospect_guestcard_minimal_profile(self) -> None:
        """Test updating a guest card with minimal required profile data."""
        prospect_id = 122124
        profile = GuestCardProfile(
            first_name="Bob",
            last_name="Smith",
        )

        result = update_prospect_guestcard(prospect_id, profile)

        assert result.profile.first_name == "Bob"
        assert result.profile.last_name == "Smith"
        assert result.profile.target_move_date is None
        assert result.profile.email is None
        assert result.profile.phone is None
        assert result.preferences is None

    def test_update_prospect_guestcard_without_preferences(self) -> None:
        """Test updating a guest card without preferences."""
        prospect_id = 122125
        profile = GuestCardProfile(
            first_name="Carol",
            last_name="Davis",
            target_move_date="2024-08-15",
            email="<EMAIL>",
        )

        result = update_prospect_guestcard(prospect_id, profile)

        assert result.profile.first_name == "Carol"
        assert result.profile.last_name == "Davis"
        assert result.profile.target_move_date == "2024-08-15"
        assert result.profile.email == "<EMAIL>"
        assert result.preferences is None


class TestProspectStatus:
    """Test cases for prospect status operations."""

    def test_update_prospect_status_success(self) -> None:
        """Test successfully updating prospect status."""
        prospect_id = 122129
        status = "qualified"
        response = client.put(f"/prospect/{prospect_id}/status?status={status}")
        assert response.status_code == 200
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["status"] == status

    def test_update_prospect_status_invalid_id(self) -> None:
        """Test updating prospect status with invalid ID."""
        status = "qualified"
        response = client.put(f"/prospect/invalid_id/status?status={status}")
        assert response.status_code == 422  # FastAPI validation error

    def test_update_prospect_status_empty_status(self) -> None:
        """Test updating prospect status with empty status."""
        prospect_id = 122130
        response = client.put(f"/prospect/{prospect_id}/status?status=")
        assert response.status_code == 200  # Empty string is valid
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["status"] == ""

    def test_update_prospect_status_special_characters(self) -> None:
        """Test updating prospect status with special characters."""
        prospect_id = 122131
        status = "in-progress (pending)"
        response = client.put(f"/prospect/{prospect_id}/status?status={status}")
        assert response.status_code == 200
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["status"] == status

    def test_update_prospect_status_missing_status_parameter(self) -> None:
        """Test updating prospect status without status parameter."""
        prospect_id = 122132
        response = client.put(f"/prospect/{prospect_id}/status")
        assert response.status_code == 422  # FastAPI validation error - missing required parameter


class TestProspectActivity:
    """Test cases for prospect activity operations."""

    def test_add_prospect_activity_success(self) -> None:
        """Test successfully adding activity to prospect."""
        prospect_id = 122133
        activity = "Viewed property listing"
        response = client.post(f"/prospect/{prospect_id}/activity?activity={activity}")
        assert response.status_code == 200
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["activity"] == activity

    def test_add_prospect_activity_invalid_id(self) -> None:
        """Test adding activity with invalid prospect ID."""
        activity = "Viewed property listing"
        response = client.post(f"/prospect/invalid_id/activity?activity={activity}")
        assert response.status_code == 422  # FastAPI validation error

    def test_add_prospect_activity_empty_activity(self) -> None:
        """Test adding empty activity to prospect."""
        prospect_id = 122134
        response = client.post(f"/prospect/{prospect_id}/activity?activity=")
        assert response.status_code == 200  # Empty string is valid
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["activity"] == ""

    def test_add_prospect_activity_long_text(self) -> None:
        """Test adding long activity text to prospect."""
        prospect_id = 122135
        activity = "This is a very long activity description that might contain detailed information about what the prospect did during their visit to the property, including their reactions and any questions they asked."
        response = client.post(f"/prospect/{prospect_id}/activity?activity={activity}")
        assert response.status_code == 200
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["activity"] == activity

    def test_add_prospect_activity_special_characters(self) -> None:
        """Test adding activity with special characters."""
        prospect_id = 122136
        activity = "Contacted via email: <EMAIL> (urgent)"
        response = client.post(f"/prospect/{prospect_id}/activity?activity={activity}")
        assert response.status_code == 200
        data = response.json()
        assert data["prospect_id"] == prospect_id
        assert data["activity"] == activity

    def test_add_prospect_activity_missing_activity_parameter(self) -> None:
        """Test adding activity without activity parameter."""
        prospect_id = 122137
        response = client.post(f"/prospect/{prospect_id}/activity")
        assert response.status_code == 422  # FastAPI validation error - missing required parameter


class TestGuestCardModels:
    """Test cases for Pydantic models used in guest card operations."""

    def test_guest_card_profile_model(self) -> None:
        """Test GuestCardProfile model validation."""
        # Test valid profile
        profile = GuestCardProfile(
            first_name="John",
            last_name="Doe",
            target_move_date="2024-09-01",
            email="<EMAIL>",
            phone={"phone_number": "******-123-4567"},
        )
        assert profile.first_name == "John"
        assert profile.last_name == "Doe"
        assert profile.target_move_date == "2024-09-01"
        assert profile.email == "<EMAIL>"
        assert profile.phone.phone_number == "******-123-4567"

    def test_guest_card_profile_model_minimal(self) -> None:
        """Test GuestCardProfile model with minimal required fields."""
        profile = GuestCardProfile(first_name="Jane", last_name="Smith")
        assert profile.first_name == "Jane"
        assert profile.last_name == "Smith"
        assert profile.target_move_date is None
        assert profile.email is None
        assert profile.phone is None

    def test_guest_card_preferences_model(self) -> None:
        """Test GuestCardPreferences model validation."""
        preferences = GuestCardPreferences(bedrooms=["1", "2", "3"])
        assert preferences.bedrooms == ["1", "2", "3"]

    def test_guest_card_preferences_model_empty(self) -> None:
        """Test GuestCardPreferences model with empty bedrooms."""
        preferences = GuestCardPreferences(bedrooms=[])
        assert preferences.bedrooms == []

    def test_guest_card_request_type_model(self) -> None:
        """Test GuestCardRequestType model validation."""
        request = GuestCardRequestType(
            profile=GuestCardProfile(first_name="Jane", last_name="Smith"),
            preferences=GuestCardPreferences(bedrooms=["2"]),
        )
        assert request.profile.first_name == "Jane"
        assert request.profile.last_name == "Smith"
        assert request.preferences.bedrooms == ["2"]

    def test_guest_card_request_type_model_without_preferences(self) -> None:
        """Test GuestCardRequestType model without preferences."""
        request = GuestCardRequestType(profile=GuestCardProfile(first_name="Jane", last_name="Smith"))
        assert request.profile.first_name == "Jane"
        assert request.profile.last_name == "Smith"
        assert request.preferences is None

    def test_prospect_status_response_model(self) -> None:
        """Test ProspectStatusResponse model validation."""
        response = ProspectStatusResponse(prospect_id=12345, status="active")
        assert response.prospect_id == 12345
        assert response.status == "active"

    def test_prospect_activity_response_model(self) -> None:
        """Test ProspectActivityResponse model validation."""
        response = ProspectActivityResponse(prospect_id=12345, activity="Scheduled tour")
        assert response.prospect_id == 12345
        assert response.activity == "Scheduled tour"

    def test_guest_card_phone_model(self) -> None:
        """Test GuestCardPhone model validation."""
        phone = {"phone_number": "******-123-4567"}
        assert phone["phone_number"] == "******-123-4567"

    def test_guest_card_phone_model_empty(self) -> None:
        """Test GuestCardPhone model with empty phone number."""
        phone = {"phone_number": None}
        assert phone["phone_number"] is None
