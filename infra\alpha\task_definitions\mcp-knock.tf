resource "local_file" "mcp_knock" {
  filename = "mcp-knock.json"

  content = jsonencode({
    containerDefinitions = [
      {
        name              = "${var.environment}-${var.application}-nginx"
        image             = "171267611104.dkr.ecr.us-east-1.amazonaws.com/nginx-proxy:proxy-pass-websocket"
        memory            = 256
        memoryReservation = 64
        cpu               = 64
        linuxParameters = {
          tmpfs = [
            {
              containerPath = "/tmp"
              mountOptions  = ["rw"]
              size          = 64
            },
            {
              containerPath = "/etc/nginx/conf.d"
              mountOptions  = ["rw"]
              size          = 1
            }
          ]
        }
        portMappings = [
          {
            containerPort = 8080,
            protocol      = "tcp"
          }
        ],
        links = [
          "${var.environment}-${var.application}:${var.application}"
        ]
        environment = [
          {
            name  = "LISTEN_PORT",
            value = "8080"
          },
          {
            name  = "APP_PORT",
            value = "8000"
          },
          {
            name  = "APP_HOST",
            value = "${var.application}"
          }
        ],
        logConfiguration = {
          logDriver = "awslogs"
          options = {
            awslogs-group         = "/ecs/${var.environment}-${var.application}"
            awslogs-region        = "us-east-1"
            awslogs-stream-prefix = "nginx"
          }
        }
      },
      {
        name   = "${var.environment}-mcp-knock"
        image  = "171267611104.dkr.ecr.us-east-1.amazonaws.com/mcp-knock:${var.app_image_tag}"
        cpu    = 128
        memory = 1024
        portMappings = [
          {
            containerPort = 8000
            protocol      = "tcp"
          }
        ]
        environment = [
          {
            name  = "ENVIRONMENT",
            value = "${var.environment}"
          }
        ]
        secrets = [for secret in local.secrets:
          {
            name = secret
            valueFrom = "arn:aws:secretsmanager:us-east-1:${var.aws_account}:secret:mcp-knock-jH5tWU:${secret}::"
          }
        ]
        logConfiguration = {
          logDriver = "awslogs"
          options = {
            awslogs-group         = "/ecs/${var.environment}-${var.application}"
            awslogs-region        = "us-east-1"
            awslogs-stream-prefix = "app"
          }
        }
      }
    ]
    family           = "${var.environment}-${var.application}"
    networkMode      = "bridge"
    taskRoleArn      = "arn:aws:iam::${var.aws_account}:role/${var.environment}-${var.application}-task-role"
    executionRoleArn = "arn:aws:iam::${var.aws_account}:role/${var.environment}-${var.application}-execution-role"
  })
}
