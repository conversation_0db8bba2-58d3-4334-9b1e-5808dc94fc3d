name: "Validate PR Title"

on:
  pull_request:
    types:
      [
        opened,
        reopened,
        edited,
        synchronize,
        ready_for_review,
        review_requested,
      ]

jobs:
  check-title:
    if: ${{ false }} # disabled
    runs-on: ubuntu-latest
    steps:
      - name: Check PR Title Format
        env:
          PR_TITLE: ${{ github.event.pull_request.title }}
        run: |
          if ! [[ "$PR_TITLE" =~ ^\[KNCK-[0-9]+\]\ -\ .+ ]]; then
            echo "PR title does not match the required format '[KNCK-XXXX] - Description'"
            exit 1
          fi
