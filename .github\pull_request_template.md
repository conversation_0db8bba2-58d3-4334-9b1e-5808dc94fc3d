-   [ ] **Ticket Format:** [KNCK-XXXX] - Description

## Description

-   Provide a summary of the changes made and the issues addressed.
-   List any dependencies required for this change.

## Type of Change

-   [ ] Bug fix (non-breaking change which fixes an issue)
-   [ ] New feature (non-breaking change that adds functionality)
-   [ ] Breaking change (fix or feature that would significantly affect existing functionality)
-   [ ] Code style update (formatting, renaming)
-   [ ] Refactoring (no functional changes, no API changes)
-   [ ] Build-related changes
-   [ ] Documentation content changes
-   [ ] Other (please describe):

## Testing Details
-   Describe the tests you performed to verify your changes.
-   Provide instructions for reproducing any relevant tests.
-   List any relevant details for your test configuration.

## Checklist
Thank you for opening a Pull Request! Before submitting your PR, there are a few things you can do to make sure it goes smoothly:
- [ ] Make sure to open an issue before writing your code! That way we can discuss the change, evaluate designs, and agree on the general idea
- [ ] Ensure the tests and linter pass
- [ ] Communicate test infrastructure changes, i.e. API enablement, secrets
- [ ] Appropriate docs were updated (if necessary)
