"""
This module provides logging configuration for the Knock MCP Server application.

It defines a `LoggingConfig` class to structure the logging setup and a
`setup_logging` function to apply the configuration.
"""

import logging
from logging.config import dictConfig

from pydantic import BaseModel

from src.config import config


class LoggingConfig(BaseModel):
    """
    Pydantic-based configuration for the application's logging.

    This class defines the structure and default values for the logging
    configuration, including formatters, handlers, and loggers.
    """

    LOGGER_NAME: str = config.APP_NAME
    LOG_FORMAT: str = "%(levelprefix)s %(asctime)s\t[%(name)s] %(message)s"
    LOG_LEVEL: str = config.LOG_LEVEL

    # Logging configuration schema
    version: int = 1
    disable_existing_loggers: bool = False
    formatters: dict = {
        "default": {
            "()": "uvicorn.logging.DefaultFormatter",
            "fmt": LOG_FORMAT,
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    }
    handlers: dict = {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
    }
    loggers: dict = {
        "": {"handlers": ["default"], "level": LOG_LEVEL},  # Root logger
        "uvicorn": {"handlers": ["default"], "level": LOG_LEVEL},
        LOGGER_NAME: {"handlers": ["default"], "level": LOG_LEVEL},
    }


def setup_logging() -> None:
    """
    Sets up the application-wide logging.

    This function instantiates the `LoggingConfig` and applies it using
    `logging.config.dictConfig`. It should be called once at application startup.
    """
    logging_config = LoggingConfig()
    dictConfig(logging_config.model_dump())


def get_logger(name: str | None = None) -> logging.Logger:
    """
    Get a logger instance for the specified name.

    Args:
        name: The name for the logger. If None, returns the root logger.

    Returns:
        A configured logger instance.
    """
    return logging.getLogger(name or config.APP_NAME)
