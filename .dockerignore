# Git
.git
.gitignore

# Docker
.dockerignore
Dockerfile

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
*.egg-info

# Virtual environment
.venv
venv

# IDE/Editor specific
.idea/
.vscode/

# Build artifacts
dist/
build/
*.egg
*.whl

# Test artifacts
.pytest_cache/
.coverage
htmlcov/
tests/

# Development tools
.mypy_cache/
.ruff_cache/
.pre-commit-config.yaml
.python-version

# Logs
logs/
*.log

# Environment files
.env
.env.*
!.env.sample

# Makefile
Makefile
.DS_Store
