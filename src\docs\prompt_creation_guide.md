# How to Create Prompts in FastApiMCP

## Overview

FastApiMCP automatically converts your FastAPI endpoints into MCP (Model Context Protocol) tools/prompts. This means you can create prompts by simply defining FastAPI endpoints with proper documentation and structure.

## Key Concepts

### 1. Automatic Conversion
FastApiMCP automatically:
- Converts FastAPI endpoints into MCP tools
- Uses endpoint documentation as tool descriptions
- Maps request/response models to tool parameters
- <PERSON>les parameter validation and type conversion

### 2. Endpoint Structure
Each FastAPI endpoint becomes an MCP tool with:
- **Name**: Derived from the endpoint function name
- **Description**: From the function's docstring
- **Parameters**: From the endpoint's parameters and request models
- **Response**: From the response model

## Basic Prompt Creation

### Step 1: Create a FastAPI Router

```python
from fastapi import APIRouter, Query
from pydantic import BaseModel

router = APIRouter(prefix="/prompts", tags=["Prompts"])
```

### Step 2: Define Request/Response Models

```python
class PromptRequest(BaseModel):
    topic: str
    style: Optional[str] = "professional"
    length: Optional[str] = "medium"

class PromptResponse(BaseModel):
    prompt: str
    metadata: dict
```

### Step 3: Create the Endpoint

```python
@router.post("/generate", response_model=PromptResponse)
async def generate_prompt(request: PromptRequest) -> PromptResponse:
    """
    Generate a prompt based on the given parameters.

    This endpoint will be automatically converted into an MCP tool.
    """
    # Your prompt generation logic here
    prompt = f"Create a {request.style} piece of content about {request.topic}"

    return PromptResponse(
        prompt=prompt,
        metadata={"topic": request.topic, "style": request.style}
    )
```

### Step 4: Include the Router in Your App

```python
from src.tools.prompts import router as prompts_router

app.include_router(prompts_router, tags=["Prompts"])
```

## Advanced Prompt Patterns

### 1. Query Parameters

```python
@router.get("/analyze")
async def analyze_prompt(
    text: str = Query(..., description="The text to analyze"),
    analysis_type: str = Query("sentiment", description="Type of analysis")
) -> dict:
    """
    Analyze a given text prompt.
    """
    # Analysis logic here
    return {"text": text, "analysis": "results"}
```

### 2. Enum Parameters

```python
from enum import Enum

class PromptStyle(str, Enum):
    PROFESSIONAL = "professional"
    CASUAL = "casual"
    TECHNICAL = "technical"

@router.post("/generate-styled")
async def generate_styled_prompt(
    topic: str,
    style: PromptStyle = PromptStyle.PROFESSIONAL
) -> dict:
    """
    Generate a prompt with specific styling.
    """
    # Style-specific logic here
    return {"prompt": f"Create a {style} piece about {topic}"}
```

### 3. Complex Request Models

```python
class AdvancedPromptRequest(BaseModel):
    topic: str = Field(..., description="The main topic")
    content_type: str = Field(..., description="Type of content")
    target_audience: str = Field("general", description="Target audience")
    key_points: Optional[List[str]] = Field(None, description="Key points to include")
    word_limit: Optional[int] = Field(None, description="Word limit")

@router.post("/generate-advanced")
async def generate_advanced_prompt(request: AdvancedPromptRequest) -> dict:
    """
    Generate an advanced prompt with multiple parameters.
    """
    # Complex prompt generation logic
    return {"prompt": "generated_prompt", "metadata": request.dict()}
```

## Best Practices

### 1. Documentation
- Always include detailed docstrings for your endpoints
- Use descriptive parameter names and descriptions
- Provide examples in your documentation

### 2. Parameter Validation
- Use Pydantic models for request/response validation
- Add Field descriptions for better tool documentation
- Use appropriate data types and constraints

### 3. Error Handling
- Return meaningful error responses
- Use proper HTTP status codes
- Include error details in responses

### 4. Naming Conventions
- Use descriptive function names
- Follow consistent naming patterns
- Use tags to group related endpoints

## Example: Complete Prompt Tool

```python
"""
Complete example of a prompt generation tool.
"""

from typing import Optional, List
from fastapi import APIRouter, Query
from pydantic import BaseModel, Field

router = APIRouter(prefix="/content-prompts", tags=["Content Prompts"])

class ContentPromptRequest(BaseModel):
    """Request model for content prompt generation."""
    topic: str = Field(..., description="The main topic for content")
    content_type: str = Field(..., description="Type of content (article, email, social)")
    audience: str = Field("general", description="Target audience")
    tone: str = Field("professional", description="Desired tone")
    key_points: Optional[List[str]] = Field(None, description="Key points to include")
    word_count: Optional[int] = Field(None, description="Target word count")

class ContentPromptResponse(BaseModel):
    """Response model for content prompt generation."""
    prompt: str = Field(..., description="The generated prompt")
    template_used: str = Field(..., description="Template used for generation")
    variables: dict = Field(..., description="Variables used in generation")
    suggestions: List[str] = Field(..., description="Suggestions for improvement")

@router.post("/generate-content", response_model=ContentPromptResponse)
async def generate_content_prompt(request: ContentPromptRequest) -> ContentPromptResponse:
    """
    Generate a content creation prompt based on specified parameters.

    This tool helps create structured prompts for various types of content
    including articles, emails, and social media posts.

    Args:
        request: The content prompt request containing topic, type, and parameters

    Returns:
        A structured prompt with metadata and suggestions
    """
    # Template selection logic
    templates = {
        "article": "Write a {tone} article about {topic} for {audience}. Include: {key_points}. Target {word_count} words.",
        "email": "Compose a {tone} email about {topic} for {audience}. Key points: {key_points}.",
        "social": "Create a {tone} social media post about {topic} for {audience}. Keep it engaging and concise."
    }

    template = templates.get(request.content_type, templates["article"])

    # Format the template
    key_points_str = ", ".join(request.key_points) if request.key_points else "relevant information"
    word_count_str = str(request.word_count) if request.word_count else "appropriate length"

    prompt = template.format(
        topic=request.topic,
        content_type=request.content_type,
        audience=request.audience,
        tone=request.tone,
        key_points=key_points_str,
        word_count=word_count_str
    )

    # Generate suggestions
    suggestions = [
        f"Consider adding specific examples for {request.topic}",
        f"Use {request.tone} language patterns",
        "Include a clear call-to-action",
        "Add relevant statistics or data points"
    ]

    return ContentPromptResponse(
        prompt=prompt,
        template_used=template,
        variables=request.dict(),
        suggestions=suggestions
    )
```

## Testing Your Prompts

### 1. Start the Server
```bash
uv run python main.py
```

### 2. Access the API Documentation
- Visit `http://localhost:8000/docs` for Swagger UI
- Visit `http://localhost:8000/redoc` for ReDoc

### 3. Test MCP Integration
- The endpoints will be available as MCP tools
- Use an MCP client to test the tools
- Check the `/mcp` endpoint for MCP-specific functionality

## Common Patterns

### 1. Template-Based Prompts
```python
@router.post("/template")
async def create_from_template(
    template: str = Body(..., description="Template string"),
    variables: Dict[str, str] = Body(..., description="Variables to substitute")
) -> dict:
    """Create a prompt from a custom template."""
    prompt = template
    for key, value in variables.items():
        prompt = prompt.replace(f"{{{key}}}", str(value))
    return {"prompt": prompt}
```

### 2. Analysis Tools
```python
@router.get("/analyze")
async def analyze_prompt(
    prompt: str = Query(..., description="Prompt to analyze"),
    include_suggestions: bool = Query(True, description="Include improvement suggestions")
) -> dict:
    """Analyze prompt effectiveness and provide suggestions."""
    # Analysis logic here
    return {"analysis": "results", "suggestions": ["suggestion1", "suggestion2"]}
```

### 3. Batch Processing
```python
@router.post("/batch")
async def generate_batch_prompts(
    requests: List[ContentPromptRequest]
) -> List[ContentPromptResponse]:
    """Generate multiple prompts in batch."""
    results = []
    for request in requests:
        # Generate prompt for each request
        result = await generate_content_prompt(request)
        results.append(result)
    return results
```

## Troubleshooting

### Common Issues

1. **Endpoint Not Appearing as MCP Tool**
   - Ensure the router is included in the main app
   - Check that the endpoint has proper documentation
   - Verify parameter types are supported

2. **Parameter Validation Errors**
   - Use Pydantic models for complex validation
   - Add proper Field descriptions
   - Test with the API documentation first

3. **Response Format Issues**
   - Ensure response models are properly defined
   - Use consistent data structures
   - Handle errors gracefully

### Debugging Tips

1. Test endpoints directly via HTTP first
2. Check the API documentation at `/docs`
3. Use logging to debug complex logic
4. Validate MCP tool descriptions in the client

## Conclusion

Creating prompts in FastApiMCP is straightforward - just create well-documented FastAPI endpoints and FastApiMCP will automatically convert them into MCP tools. Focus on good documentation, proper validation, and clear parameter definitions for the best results.
