name: Python CI

on:
  push:
    branches-ignore:
      - main
  pull_request:
    branches-ignore:
      - main

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true

      - name: "Set up Python"
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install project dependencies
        run: uv sync --frozen --all-extras --dev

      # - name: Run linter
      #   run: uv run ruff check src/

      # - name: Run format check
      #   run: uv run ruff format --check src/

      # - name: Run type checking (mypy)
      #   run: uv run --frozen mypy src/

      - name: Run tests with coverage
        run: uv run --frozen pytest --cov=src --cov-report=term-missing
