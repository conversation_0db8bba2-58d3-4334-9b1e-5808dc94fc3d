# Logging Guide for Knock MCP Server

This guide covers how to use logging effectively in the Knock MCP Server tools and application.

## Table of Contents

1. [Basic Setup](#basic-setup)
2. [Log Levels](#log-levels)
3. [Logger Configuration](#logger-configuration)
4. [Logging Patterns](#logging-patterns)
5. [Best Practices](#best-practices)
6. [Examples](#examples)

## Basic Setup

### Importing the Logger

In your tool files, import the logger like this:

```python
from src.utils.log_config import get_logger

logger = get_logger(__name__)
```

### Why `__name__`?

Using `__name__` creates a logger specific to your module, which helps with:
- Filtering logs by module
- Understanding where logs are coming from
- Better log organization

## Log Levels

The logging system supports these levels (from lowest to highest priority):

1. **DEBUG** (10) - Detailed information for debugging
2. **INFO** (20) - General information about program execution
3. **WARNING** (30) - Indicates a potential problem
4. **ERROR** (40) - A more serious problem
5. **CRITICAL** (50) - A critical problem that may prevent the program from running

### When to Use Each Level

- **DEBUG**: Detailed information useful for debugging
  ```python
  logger.debug(f"Processing user data: {user_data}")
  ```

- **INFO**: General information about normal operations
  ```python
  logger.info(f"User {user_id} logged in successfully")
  ```

- **WARNING**: Something unexpected happened, but the program can continue
  ```python
  logger.warning(f"Large payload detected: {len(data)} items")
  ```

- **ERROR**: A serious problem occurred
  ```python
  logger.error(f"Failed to connect to database: {str(e)}")
  ```

- **CRITICAL**: A critical error that may prevent the program from running
  ```python
  logger.critical("Database connection lost - application may be unstable")
  ```

## Logger Configuration

The logging configuration is defined in `src/utils/log_config.py` and includes:

- **Formatters**: Define how log messages are formatted
- **Handlers**: Define where logs are sent (console, file, etc.)
- **Loggers**: Define which loggers exist and their settings

### Configuration Features

- **Structured Format**: `%(levelprefix)s %(asctime)s\t[%(name)s] %(message)s`
- **Timestamp**: ISO format timestamps
- **Module Names**: Shows which module generated the log
- **Log Levels**: Configurable via environment variables

## Logging Patterns

### 1. Basic Logging

```python
logger.info(f"Processing request for user_id: {user_id}")
logger.debug(f"Request data: {request_data}")
```

### 2. Error Handling with Logging

```python
try:
    # Your code here
    result = process_data(data)
    logger.info("Data processed successfully")
    return result
except Exception as e:
    logger.error(f"Failed to process data: {str(e)}")
    logger.exception("Full exception details:")
    raise
```

### 3. Performance Logging

```python
import time

start_time = time.time()
# ... your code ...
processing_time = time.time() - start_time
logger.info(f"Operation completed in {processing_time:.3f} seconds")
```

### 4. Conditional Logging

```python
# Only log if debug level is enabled
if logger.isEnabledFor(10):  # DEBUG level
    expensive_data = compute_expensive_data()
    logger.debug(f"Expensive data: {expensive_data}")
```

### 5. Structured Logging

```python
# Log related information together
logger.info(f"User action: {action}, User ID: {user_id}, Timestamp: {timestamp}")
```

## Best Practices

### 1. Use Appropriate Log Levels

- Use DEBUG for detailed debugging information
- Use INFO for normal operations
- Use WARNING for potential issues
- Use ERROR for actual problems
- Use CRITICAL sparingly

### 2. Include Context

Always include relevant context in your log messages:

```python
# Good
logger.info(f"Processing order {order_id} for customer {customer_id}")

# Bad
logger.info("Processing order")
```

### 3. Avoid Sensitive Information

Never log sensitive data like passwords, API keys, or personal information:

```python
# Good
logger.info(f"User {user_id} authenticated successfully")

# Bad
logger.info(f"User {user_id} authenticated with password: {password}")
```

### 4. Use Exception Logging

When catching exceptions, use `logger.exception()` to automatically include the stack trace:

```python
try:
    # Your code
except Exception as e:
    logger.exception("An error occurred")  # Includes stack trace
    # or
    logger.error(f"An error occurred: {str(e)}")  # Just the message
```

### 5. Performance Considerations

- Use conditional logging for expensive operations
- Avoid string formatting in debug logs if the level is disabled
- Use lazy evaluation for complex data structures

```python
# Good - only formats if debug is enabled
if logger.isEnabledFor(10):
    logger.debug(f"Complex data: {expensive_computation()}")

# Bad - always computes expensive_computation()
logger.debug(f"Complex data: {expensive_computation()}")
```

### 6. Consistent Naming

Use consistent naming patterns for your loggers:

```python
# Use module name
logger = get_logger(__name__)

# Or use a specific name for the tool
logger = get_logger("knock.property_info")
```

## Examples

### Complete Tool Example

```python
"""
Example tool with comprehensive logging.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from src.utils.log_config import get_logger

router = APIRouter()
logger = get_logger(__name__)


class ExampleRequest(BaseModel):
    user_id: int
    data: dict


@router.post("/example")
async def example_endpoint(request: ExampleRequest):
    """
    Example endpoint with comprehensive logging.
    """
    logger.info(f"Processing request for user_id: {request.user_id}")
    logger.debug(f"Request data: {request.data}")

    try:
        # Validate input
        if not request.data:
            logger.warning(f"Empty data received for user_id: {request.user_id}")
            raise HTTPException(status_code=400, detail="Empty data")

        # Process data
        logger.info("Starting data processing...")
        result = process_data(request.data)

        logger.info(f"Successfully processed data for user_id: {request.user_id}")
        return {"success": True, "data": result}

    except HTTPException:
        # Re-raise HTTP exceptions without additional logging
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing request: {str(e)}")
        logger.exception("Full exception details:")
        raise HTTPException(status_code=500, detail="Internal server error")


def process_data(data: dict) -> dict:
    """
    Example utility function with logging.
    """
    logger.debug("Processing data...")

    try:
        # Your processing logic here
        processed = {k: str(v).upper() for k, v in data.items()}
        logger.info(f"Processed {len(data)} items")
        return processed
    except Exception as e:
        logger.error(f"Error processing data: {str(e)}")
        raise
```

### Health Check Example

```python
@router.get("/health")
async def health_check():
    """
    Health check with logging.
    """
    logger.debug("Health check requested")

    checks = {
        "database": check_database(),
        "cache": check_cache(),
        "external_api": check_external_api()
    }

    # Log each check result
    for service, status in checks.items():
        if status == "healthy":
            logger.debug(f"Health check passed for {service}")
        else:
            logger.warning(f"Health check failed for {service}: {status}")

    all_healthy = all(status == "healthy" for status in checks.values())

    if all_healthy:
        logger.info("All health checks passed")
    else:
        logger.warning("Some health checks failed")

    return {"status": "healthy" if all_healthy else "unhealthy", "checks": checks}
```

## Configuration

The logging configuration can be customized by modifying `src/utils/log_config.py`. Key configuration options:

- **LOG_LEVEL**: Set via environment variable (default: INFO)
- **LOG_FORMAT**: Customize log message format
- **Handlers**: Add file logging, remote logging, etc.
- **Filters**: Add custom filters for specific log messages

## Environment Variables

Set these environment variables to configure logging:

```bash
# Set log level
export LOG_LEVEL=DEBUG

# Set application name (used in logger names)
export APP_NAME=knock-mcp-server
```

## Troubleshooting

### Common Issues

1. **No logs appearing**: Check if logging is set up in main.py
2. **Wrong log level**: Verify LOG_LEVEL environment variable
3. **Missing context**: Ensure you're including relevant information in log messages
4. **Performance issues**: Use conditional logging for expensive operations

### Debug Mode

To enable debug logging, set the environment variable:

```bash
export LOG_LEVEL=DEBUG
```

This will show all log levels including DEBUG messages.
