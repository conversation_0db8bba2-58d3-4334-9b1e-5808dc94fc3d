import logging

from fastapi import FastAPI
from opentelemetry import _events, _logs, metrics, trace
from opentelemetry.exporter.otlp.proto.grpc._log_exporter import OTLPLogExporter
from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter

# GRPC Exporters
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.aiohttp_client import AioHttpClientInstrumentor
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.openai_v2 import OpenAIInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.sdk._events import EventLoggerProvider
from opentelemetry.sdk._logs import LoggerProvider, LoggingHandler
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

from src.config import config


def setup_opentelemetry(app: FastAPI) -> None:
    """
    Set up OpenTelemetry for tracing and logging.
    """
    if config.OTEL_ENABLED:
        _setup_logging()
        _setup_tracing()
        _setup_metrics()

        FastAPIInstrumentor.instrument_app(app)
        HTTPXClientInstrumentor().instrument()
        OpenAIInstrumentor().instrument()
        RequestsInstrumentor().instrument()
        AioHttpClientInstrumentor().instrument()


def _setup_logging() -> None:
    """
    Set up the OpenTelemetry LoggerProvider and LogRecordProcessor.
    """
    root_logger = logging.getLogger()
    logger_provider = LoggerProvider()
    logger_processor = BatchLogRecordProcessor(OTLPLogExporter())
    logger_provider.add_log_record_processor(logger_processor)

    handler = LoggingHandler(logger_provider=logger_provider)
    root_logger.addHandler(handler)
    event_logger_provider = EventLoggerProvider(logger_provider=logger_provider)

    _logs.set_logger_provider(logger_provider)
    _events.set_event_logger_provider(event_logger_provider)


def _setup_tracing() -> None:
    """
    Set up the OpenTelemetry TracerProvider and SpanProcessor.
    """
    trace_provider = TracerProvider()
    span_processor = BatchSpanProcessor(OTLPSpanExporter())
    trace_provider.add_span_processor(span_processor)
    trace.set_tracer_provider(trace_provider)


def _setup_metrics() -> None:
    """
    Set up the OpenTelemetry MeterProvider and MetricReader.
    """
    metric_reader = PeriodicExportingMetricReader(OTLPMetricExporter())
    meter_provider = MeterProvider(metric_readers=[metric_reader])
    metrics.set_meter_provider(meter_provider)
