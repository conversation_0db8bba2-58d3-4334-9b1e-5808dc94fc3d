module "ecs_service" {
  source              = "**************:knockrentals/tf-ecs-service//?ref=v3.0.21"
  accepts_connections = true
  is_internal_service = false
  application         = var.application
  environment         = var.environment
  cluster_name        = var.cluster
  vpc_name            = var.vpc_name
  is_awsvpc           = "false"
  akamai_enabled      = "false"

  service_static_count      = 1

  container_target_port      = 8080
  container_target_name      = "${var.environment}-${var.application}-nginx"
  deregistration_delay       = 5
  container_healthcheck_path = "/api/health"

  external_alb_name  = "${var.environment}-external-alb"
  internal_alb_name  = "${var.environment}-internal-alb"
  host_header_values = var.host_header_values

  task_role_policy = []

  execution_role_policy = [{
    Effect   = "Allow"
    Action   = ["ssm:GetParameters"]
    Resource = ["arn:aws:ssm:us-east-1:${data.aws_caller_identity.current.account_id}:parameter/${var.environment}/${var.application}/*"]
  },
  {
    Effect   = "Allow"
    Action   = ["secretsmanager:DescribeSecret","secretsmanager:GetSecretValue"]
    Resource = ["arn:aws:secretsmanager:us-east-1:${data.aws_caller_identity.current.account_id}:secret:mcp-knock-jH5tWU"]
  }
]

  github_repo_name = var.github_repo_name
  image_repo_names = var.image_repo_names
  github_org          = var.github_org

  additional_github_upload_permissions = []
  additional_github_deploy_permissions = []

  providers = {
    aws.dns = aws.shared-networking
    aws.ecr = aws.ecr
  }
}
