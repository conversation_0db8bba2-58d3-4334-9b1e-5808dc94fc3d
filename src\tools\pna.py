import datetime
import logging
import time
from enum import Enum

from fastapi import APIRouter, HTTPException, Query
from fastmcp import <PERSON>MC<PERSON>
from pydantic import BaseModel
from starlette import status

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

router = APIRouter()


class AvailabilityStatus(str, Enum):
    matched = "matched"
    no_bed_match_future = "no_bed_match_future"
    no_bed_match_now = "no_bed_match_now"
    future = "future"
    unavailable = "unavailable"


class AvailabilityResponse(BaseModel):
    availability: AvailabilityStatus
    pricing_link: str | None
    requested_bedrooms: int | str
    availability_price: str | None
    availability_date: datetime.date | None  # datetime.date
    availability_bedrooms: int | str | None  # can this be int, str or None? examples are studio, 1, 2, 3, ...


class InvalidBedroomsError(ValueError):
    def __init__(self, value: str):
        super().__init__(f"Bedrooms value '{value}' is invalid. Must be 'studio' or a positive integer.")


def validate_bedrooms(value: str) -> int | str:
    if value == "studio":
        return value
    if value.isdigit() and int(value) > 0:
        return int(value)
    raise InvalidBedroomsError(value)


def check_pricing_and_availability(
    property_id: int,
    bedrooms: str,
    move_date: datetime.date | None = None,
) -> AvailabilityResponse:
    if property_id is None:
        return {
            "availability": AvailabilityStatus.unavailable,
            "error": "Property ID is required.",
        }
    try:
        validated_bedrooms = validate_bedrooms(bedrooms)
    except InvalidBedroomsError as e:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)) from e

    logger.info(
        "Checking availability for property %s with bedrooms %s and move date %s",
        property_id,
        validated_bedrooms,
        move_date,
    )

    return AvailabilityResponse(
        availability=AvailabilityStatus.matched,
        pricing_link="https://example.com/pricing",
        requested_bedrooms=validated_bedrooms,
        availability_price="1500",
        availability_date=datetime.date(2025, 7, 8),
        availability_bedrooms="2",
    )


def register_pna_tools(mcp: FastMCP) -> None:
    @mcp.tool(
        name="pricing_and_availability",
        description="Check the pricing and availability for a given property and bedroom configuration.",
    )
    def pricing_and_availability(
        property_id: int,
        bedrooms: str,
        move_date: datetime.date | None = None,
    ) -> AvailabilityResponse:
        time.sleep(1)
        return check_pricing_and_availability(property_id, bedrooms, move_date)


@router.get(
    "/availability",
    response_model=AvailabilityResponse,
)
async def pricing_and_availability(
    property_id: int = Query(..., description="The ID of the property to check availability for."),
    bedrooms: str = Query(..., description="The number of bedrooms to check availability for."),
    move_date: datetime.date | None = Query(None, description="The move-in date to check availability for."),  # noqa: B008
) -> AvailabilityResponse:
    return check_pricing_and_availability(property_id, bedrooms, move_date)
