import os

secret_name = "mcp-knock"


def is_true(name: str) -> bool:
    s = name.upper()
    return s in ["TRUE", "T", "ON", "YES", "Y"]


OTEL_ENABLED = is_true(os.getenv("OTEL_ENABLED", ""))
OTEL_ENDPOINT = os.getenv("OTEL_ENDPOINT", "http://localhost:4318")
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()

ENVIRONMENT = os.getenv("ENVIRONMENT", "local")
APP_NAME = os.getenv("APP_NAME", "mcp-knock")
