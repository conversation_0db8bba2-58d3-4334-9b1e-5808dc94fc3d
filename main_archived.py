"""
Main application file for the Knock MCP Server.

This file initializes the FastAPI application, sets up logging,
configures the MCP server, and defines the entry point for running the application.
"""

import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi_mcp import FastApiMCP
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from tools.guestcard_tools import router as guest_card_tools_router
from utils.logging import setup_logging

from src.routes.router import router as health_check_router
from src.tools.get_property_info import router as get_property_info_router
from src.tools.pna import router as pricing_and_availability_router

# Load environment variables from a .env file
load_dotenv()

# Initialize the FastAPI application
# This instance will be the core of your API.
app = FastAPI(
    title="Knock MCP Server",
    description="A server for the Model Context Protocol (MCP) system.",
    version="0.0.1",
)
FastAPIInstrumentor.instrument_app(app)

# Include the health check router
# This makes the /api/health-check endpoint available.
app.include_router(health_check_router, prefix="/api", tags=["Health Check"])

# Include the guest card tools router
# This makes the guest card endpoints available and converts them to MCP tools.
app.include_router(guest_card_tools_router, prefix="/guestcard")

# Include the pricing and availability router
# This makes the pricing and availability endpoints available and converts them to MCP tools.
app.include_router(pricing_and_availability_router, prefix="/pna", tags=["Pricing and Availability"])

# Include the property info router
# This makes the property info endpoints available and converts them to MCP tools.
app.include_router(get_property_info_router, prefix="/propertyinfo", tags=["Knock Property Info"])

# Set up application-wide logging
setup_logging()

# Create and configure the MCP server
# This integrates the FastAPI application with the MCP system.
mcp = FastApiMCP(app, name="knock-mcp", description="Knock MCP Server")
mcp.mount(app)


def main() -> None:
    """
    Main entry point for running the application in development mode.

    This function mounts the MCP server to the FastAPI app and starts the Uvicorn server
    with auto-reloading enabled, making it ideal for local development.
    """
    # Mount the MCP server to the FastAPI application
    # This exposes the MCP-specific endpoints.
    # Start the Uvicorn server
    # The application will be served on 0.0.0.0:8000, making it accessible
    # from the local network. The `reload=True` flag enables auto-reloading
    # during development, and should not be used in production.
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)


# This block ensures that the main function is called only when the script
# is executed directly (e.g., `python main.py`), making it a convenient
# entry point for development.
if __name__ == "__main__":
    main()
