"""Unit tests for LDP Renter DataHub property info endpoint."""

from unittest.mock import MagicMock

import pytest
from fastapi.testclient import TestClient

from main import app
from src.models.ldp_renter_datahub_property_info import (
    LDPRenterDatahubPropertyResponse,
)

client = TestClient(app)


def test_get_ldp_renter_datahub_property_info_success() -> None:
    """Test successful property info retrieval."""
    # Test with valid property ID
    response = client.get("/propertyinfo/ldp-renter-datahub/123")
    assert response.status_code == 200

    data = response.json()

    # Verify metadata
    assert data["metadata"]["next_offset"] == 0
    assert data["metadata"]["query_time_ms"] == 426
    assert data["metadata"]["row_count"] == 1

    # Verify records structure
    assert len(data["records"]) == 1
    record = data["records"][0]
    assert record["property_id"] == "123"
    assert record["property_source"] == "LDP"
    assert record["processed_at"] is None
    assert record["extras"] == {}
    assert record["applicant_summary"] is None
    assert record["prospect_summary"] is None

    # Verify collected data
    assert len(record["collected_data"]) == 1
    collected = record["collected_data"][0]
    assert collected["data_source"] == "LeaseStar"
    assert collected["applicant"]["filtered_data"] is None
    assert collected["applicant"]["summary"] is None
    assert collected["resident"]["filtered_data"] is None
    assert collected["resident"]["summary"] is None

    # Verify filtered data
    filtered = collected["prospect"]["filtered_data"]
    assert filtered["active"] is True
    assert filtered["buildingType"] == "apartment"
    assert filtered["name"] == "Property 123"
    assert filtered["propertyType"] == "Conventional"

    # Verify office hours
    hours = filtered["propertyOfficeHours"]
    assert len(hours) == 6  # Monday through Saturday
    assert hours[0]["dayOfWeek"] == "Monday"
    assert hours[0]["startTime1"] == "09:00"
    assert hours[0]["endTime1"] == "18:00"


def test_get_ldp_renter_datahub_property_info_invalid_id() -> None:
    """Test property info retrieval with invalid property ID."""
    # Test with string ID
    response = client.get("/propertyinfo/ldp-renter-datahub/abc")
    assert response.status_code == 422
    data = response.json()
    assert "Input should be a valid integer" in data["detail"][0]["msg"]

    # Test with zero ID
    response = client.get("/propertyinfo/ldp-renter-datahub/0")
    assert response.status_code == 422
    data = response.json()
    assert "Property ID must be a positive integer" in data["detail"]

    # Test with negative ID
    response = client.get("/propertyinfo/ldp-renter-datahub/-1")
    assert response.status_code == 422
    data = response.json()
    assert "Property ID must be a positive integer" in data["detail"]


def test_get_ldp_renter_datahub_property_info_missing_id() -> None:
    """Test property info retrieval with missing property ID."""
    response = client.get("/propertyinfo/ldp-renter-datahub/")
    assert response.status_code == 404  # FastAPI returns 404 for missing path parameter


def test_ldp_renter_datahub_property_info_model_validation() -> None:
    """Test model validation for property info response."""
    response = client.get("/propertyinfo/ldp-renter-datahub/123")
    assert response.status_code == 200

    # Validate that the response can be parsed into our Pydantic model
    data = response.json()
    try:
        LDPRenterDatahubPropertyResponse(**data)
    except Exception as e:
        pytest.fail(f"Response data failed Pydantic validation: {e}")


def test_ldp_renter_datahub_property_info_mcp_tool() -> None:
    """Test MCP tool registration for property info."""
    from src.tools.property_info import register_property_info_tools

    # Create mock MCP instance
    mock_mcp = MagicMock()
    register_property_info_tools(mock_mcp)

    # Verify tool registration
    tool_registration = mock_mcp.tool.call_args_list
    ldp_tool_call = [call for call in tool_registration if call[1]["name"] == "get_ldp_renter_datahub_property_info"][0]

    assert ldp_tool_call[1]["name"] == "get_ldp_renter_datahub_property_info"
    assert "Get property info from LDP Renter DataHub" in ldp_tool_call[1]["description"]
