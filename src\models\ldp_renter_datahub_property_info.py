"""Models for LDP Renter DataHub property info responses."""

from datetime import datetime
from typing import Any

from pydantic import BaseModel


class DataSourceInfo(BaseModel):
    """Information from a specific data source."""

    filtered_data: dict[str, Any] | None = None
    summary: Any | None = None


class CollectedData(BaseModel):
    """Collected data from various sources."""

    applicant: DataSourceInfo | None = None
    data_source: str
    prospect: DataSourceInfo
    raw_data: dict[str, Any] | None = None
    resident: DataSourceInfo | None = None


class Metadata(BaseModel):
    """Response metadata."""

    next_offset: int
    query_time_ms: int
    row_count: int


class PropertyRecord(BaseModel):
    """Individual property record."""

    applicant_summary: Any | None = None
    collected_data: list[CollectedData]
    extras: dict[str, Any] | None = None
    processed_at: datetime | None = None
    property_id: str | None = None
    property_source: str | None = None
    prospect_summary: Any | None = None
    resident_summary: Any | None = None


class LDPRenterDatahubPropertyResponse(BaseModel):
    """Main response model."""

    metadata: Metadata
    records: list[PropertyRecord]
