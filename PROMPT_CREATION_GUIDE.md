# How to Create Prompts

## Overview

FastApiMCP automatically converts your FastAPI endpoints into MCP (Model Context Protocol) tools/prompts. You create prompts by defining FastAPI endpoints with proper documentation.

## Basic Steps

### 1. Create FastAPI Endpoints
```python
from fastapi import APIRouter, Query
from pydantic import BaseModel

router = APIRouter(prefix="/prompts", tags=["Prompts"])

class PromptRequest(BaseModel):
    topic: str
    style: str = "professional"

@router.post("/generate")
async def generate_prompt(request: PromptRequest) -> dict:
    """
    Generate a prompt based on the given parameters.
    This endpoint will be automatically converted into an MCP tool.
    """
    prompt = f"Create a {request.style} piece about {request.topic}"
    return {"prompt": prompt}
```

### 2. Include Router in Main App
```python
from src.tools.prompts import router as prompts_router
app.include_router(prompts_router, tags=["Prompts"])
```

### 3. FastApiMCP Automatically Converts
- Endpoints become MCP tools
- Function docstrings become tool descriptions
- Request/response models become tool parameters

## Key Features

- **Automatic Conversion**: No manual MCP tool definition needed
- **Parameter Validation**: Uses Pydantic models
- **Documentation**: Docstrings become tool descriptions
- **Type Safety**: Full type checking and validation

## Examples in This Project

1. **Basic Prompts** (`src/tools/prompts.py`)
   - Simple prompt generation
   - Text analysis
   - Prompt optimization

2. **Advanced Prompts** (`src/tools/advanced_prompts.py`)
   - Complex parameter handling
   - Template-based generation
   - Prompt analysis and suggestions

## Testing

1. Start the server: `uv run python main.py`
2. Visit `http://localhost:8000/docs` for API documentation
3. The endpoints will be available as MCP tools

## Best Practices

- Use descriptive docstrings
- Define proper Pydantic models
- Include parameter descriptions
- Handle errors gracefully
- Use consistent naming conventions
