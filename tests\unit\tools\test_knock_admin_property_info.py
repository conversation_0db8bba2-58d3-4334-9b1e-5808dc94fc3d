from datetime import datetime
from unittest.mock import MagicMock

from fastapi.testclient import TestClient

from main import app
from src.models.knock_admin_property_info import (
    AdminProperty,
    AdminPropertyData,
    AdminPropertyResponse,
    AffordableHouseProgram,
    AffordableHousing,
    Amenity,
    AmenityTypes,
    BusinessHours,
    Costable,
    CustomFee,
    Description,
    FeaturedAmenities,
    FeaturedAmenityType,
    MessageTemplates,
    PreferencesSettings,
    PropertyPreferences,
    Settings,
    SnippetConfig,
    Utilities,
    UtilityType,
    Video,
    Website,
)
from src.models.knock_non_admin_property_info import (
    Address,
    Application,
    Doorway,
    GeoLocation,
    KeySellingPoints,
    Leasing,
    LeasingTerms,
    Location,
    PetPolicy,
    Pets,
    Social,
)

client = TestClient(app)


def test_get_admin_property_info_endpoint() -> None:
    """Test the GET /propertyinfo/get_knock_admin_property_info/{property_id} endpoint"""
    property_id = 123
    response = client.get(f"/propertyinfo/admin/{property_id}")

    assert response.status_code == 200
    data = response.json()

    # Verify response structure
    assert "property" in data
    assert "settings" in data
    assert "status_code" in data
    assert data["status_code"] == "ok"

    # Verify property data
    property_data = data["property"]
    assert property_data["id"] == property_id
    assert "created_time" in property_data
    assert datetime.fromisoformat(property_data["created_time"])  # Validate datetime format
    assert "modified_time" in property_data
    assert datetime.fromisoformat(property_data["modified_time"])  # Validate datetime format
    assert "data" in property_data
    assert "custom_fees" in property_data
    assert "preferences" in property_data

    # Verify custom fees
    custom_fees = property_data["custom_fees"]
    assert len(custom_fees) > 0
    assert all(isinstance(fee["amount"], (int, float)) for fee in custom_fees)
    assert all(isinstance(fee["name"], str) for fee in custom_fees)
    assert all(fee["property_id"] == property_id for fee in custom_fees)

    # Verify nested property data
    nested_data = property_data["data"]
    assert nested_data["property_id"] == property_id
    assert "affordableHousing" in nested_data
    assert "amenities" in nested_data
    assert "featured_amenities" in nested_data
    assert "utilities" in nested_data
    assert "videos" in nested_data
    assert "views" in nested_data
    assert "website" in nested_data

    # Verify settings
    settings = data["settings"]
    assert "hours" in settings
    assert "message_templates" in settings
    assert "preferences" in settings
    assert "snippet" in settings
    assert "snippetConfig" in settings


def test_admin_property_response_model() -> None:
    """Test the AdminPropertyResponse Pydantic model"""
    # Create a minimal valid admin property response
    property_response = AdminPropertyResponse(
        property=AdminProperty(
            created_time=datetime.now().isoformat(),
            custom_fees=[
                CustomFee(amount=100.0, name="Admin Fee", property_id=123),
                CustomFee(amount=150.0, name="Cleaning Fee", property_id=123),
            ],
            data=AdminPropertyData(
                affordableHousing=AffordableHousing(
                    notes="Test notes",
                    programs=[
                        AffordableHouseProgram(available=True, name="Section 8"),
                    ],
                ),
                amenities=AmenityTypes(
                    additional=[Amenity(available=True, name="Hardwood floor")],
                    exterior=[Amenity(available=True, name="Pool")],
                    heatingAndCooling=[Amenity(available=True, name="AC")],
                    recreation=[Amenity(available=True, name="Gym")],
                    security=[Amenity(available=True, name="Gate")],
                    wiring=[Amenity(available=True, name="Internet")],
                ),
                appliances=[Amenity(available=True, name="Dishwasher")],
                costables=[Costable(isCovered=False, name="Test Cost")],
                created_time=datetime.now().isoformat(),
                debug={},
                description=Description(short="Test description"),
                doorway=Doorway(
                    agentGuidedTourDisclaimerText="",
                    applyNowIsActive=True,
                    availabilityIsActive=True,
                    bannerText="Test",
                    customAppointmentMessage="",
                    dynamicNumberInsertionType="",
                    excludeAppointmentMessagePhone=False,
                    faqsIsActive=True,
                    formattedNumber="",
                    formattedNumberIsActive=False,
                    galleryIsActive=True,
                    hideKnockBranding=False,
                    hidePricing=False,
                    hideUnitPreferences=False,
                    hideUnitSelector=False,
                    includeInQuickLaunchMenuList=[],
                    leasingInfoIsActive=True,
                    leasingSpecialIsActive=False,
                    limitAvailableUnitsShown=False,
                    liveVideoTourDisclaimerText="",
                    neighborhoodIsActive=True,
                    petInfoIsActive=True,
                    renterPortalIsActive=True,
                    requestTextIsActive=True,
                    residentPortalURL="https://example.com",
                    scheduleATourContentBox="",
                    selfGuidedTourDisclaimerText="",
                    showNoteInput=False,
                    somethingElseIsActive=True,
                    useCustomWebsite=False,
                    useDynamicFloorplans=True,
                ),
                editorType="manual",
                extra={},
                featured_amenities=FeaturedAmenities(
                    gym=FeaturedAmenityType(types=[Amenity(available=True, name="Gym")]),
                    laundry=FeaturedAmenityType(types=[Amenity(available=True, name="In Unit")]),
                    parking=FeaturedAmenityType(types=[Amenity(available=True, name="Garage")]),
                    pets=FeaturedAmenityType(types=[Amenity(available=True, name="Dogs")]),
                    pool=FeaturedAmenityType(types=[Amenity(available=True, name="Pool")]),
                ),
                furnishing=[Amenity(available=True, name="Unfurnished")],
                id="test_id",
                key_selling_points=KeySellingPoints(
                    community=["Test point"],
                    location=["Test point"],
                    units=["Test point"],
                ),
                leasing=Leasing(
                    application=Application(
                        fee="$50",
                        instructions="Test",
                        isDefaultApplicationUrlOverridden=False,
                        link="",
                    ),
                    provider="Test",
                    qualificationCriteria="Test",
                    terms=LeasingTerms(
                        breakPenalty="Test",
                        deposit="$500",
                        includeUpcharges=False,
                        leaseLengths=[],
                        leasingSpecial="Test",
                        notes="",
                    ),
                ),
                location=Location(
                    address=Address(
                        city="Test",
                        house="",
                        neighborhood="Test",
                        raw="Test",
                        state="CA",
                        street="Test",
                        zip="12345",
                    ),
                    geo=GeoLocation(coordinates=[-122.0, 37.0], type="point"),
                    name="Test",
                    needs_review=False,
                    numberOfUnits=100,
                    timezone="America/Los_Angeles",
                    yearBuilt=2020,
                ),
                logos=[],
                notes="",
                pets=Pets(
                    allowed=PetPolicy(cats=True, large_dogs=False, none=False, small_dogs=True),
                    deposit="$300",
                    fee="$50",
                    notes="Test",
                    rent="$30",
                ),
                property_id=123,
                social=Social(
                    facebook="",
                    knock_email="<EMAIL>",
                    shortlink="test",
                    website="test",
                ),
                updated_time=datetime.now().isoformat(),
                utilities=Utilities(
                    estimatedCost="",
                    types=[UtilityType(included=True, name="Water")],
                ),
                videos=[Video(thumb_url="", url="test")],
                views=[Amenity(available=True, name="City")],
                website=Website(primaryColor="#000000"),
            ),
            id=123,
            is_deleted=False,
            leasing_team_id=1000,
            modified_time=datetime.now().isoformat(),
            owning_manager_id=2000,
            preferences=PropertyPreferences(
                created_time=datetime.now().isoformat(),
                disable_pricing_availability=False,
                enable_affordable_housing=False,
                enable_selfie_scan=False,
                id=1,
                is_deleted=False,
                modified_time=datetime.now().isoformat(),
                preferences={},
                property_id=123,
                require_lease_term=False,
                require_preferred_bedroom_count=False,
                require_preferred_unit=False,
                require_prospect_floorplan=False,
                require_prospect_move_in_date=False,
                require_unit_scheduling=False,
                suppressed_autoresponders=[],
                suppressed_outbound_channels=[],
            ),
            public_id="test",
            resource_id=3000,
            timezone="America/Los_Angeles",
            type="multi-family",
        ),
        settings=Settings(
            hours={
                "1": BusinessHours(end_time="17:00:00", is_active=True, start_time="09:00:00"),
            },
            message_templates=MessageTemplates(
                away="Test",
                away_response="Test",
                greeting="Test",
                guest="Test",
            ),
            preferences=PreferencesSettings(
                button_color="#000000",
                button_greeting="Test",
                display_launcher_button=True,
                emit_analytics_events=False,
                emit_tag_manager_events=False,
                messaging_enabled=True,
                primary_color="#000000",
                script_major_version="3",
            ),
            snippet="test",
            snippetConfig=SnippetConfig(
                community_id="test",
                host="test",
                token="test",
            ),
        ),
        status_code="ok",
    )

    # Test basic properties
    assert property_response.property.id == 123
    assert property_response.status_code == "ok"

    # Test custom fees
    custom_fees = property_response.property.custom_fees
    assert len(custom_fees) == 2
    assert custom_fees[0].amount == 100.0
    assert custom_fees[0].name == "Admin Fee"

    # Test amenities
    amenities = property_response.property.data.amenities
    assert len(amenities.additional) == 1
    assert amenities.additional[0].name == "Hardwood floor"
    assert amenities.additional[0].available is True

    # Test featured amenities
    featured = property_response.property.data.featured_amenities
    assert featured.gym.types[0].name == "Gym"
    assert featured.pool.types[0].name == "Pool"

    # Test settings
    settings = property_response.settings
    assert len(settings.hours) == 1
    assert settings.message_templates.greeting == "Test"
    assert settings.preferences.button_color == "#000000"


def test_invalid_admin_property_id() -> None:
    """Test the admin endpoint with invalid property IDs"""
    # Test with string ID
    response = client.get("/propertyinfo/admin/invalid_id")
    assert response.status_code == 422  # FastAPI validation error
    error_detail = response.json()["detail"][0]
    assert error_detail["msg"] == "Input should be a valid integer, unable to parse string as an integer"
    assert error_detail["type"] == "int_parsing"
    assert error_detail["input"] == "invalid_id"

    # Test with zero ID
    response = client.get("/propertyinfo/admin/0")
    assert response.status_code == 422
    assert "Property ID must be a positive integer" in response.json()["detail"]

    # Test with negative ID
    response = client.get("/propertyinfo/admin/-1")
    assert response.status_code == 422
    assert "Property ID must be a positive integer" in response.json()["detail"]


def test_nonexistent_admin_property_id() -> None:
    """Test the admin endpoint with a property ID that doesn't exist"""
    response = client.get("/propertyinfo/admin/999999")
    assert response.status_code == 200  # Since we're using mock data, it should still return 200
    data = response.json()

    # Even non-existent IDs should return a valid response with that ID
    assert data["property"]["id"] == 999999
    assert data["status_code"] == "ok"

    # Verify the mock data is properly structured
    property_data = data["property"]["data"]
    assert property_data["property_id"] == 999999
    assert property_data["id"] == "999999"
    assert property_data["location"]["name"] == "Property 999999"
    assert datetime.fromisoformat(data["property"]["created_time"])
    assert datetime.fromisoformat(data["property"]["modified_time"])

    # Verify admin-specific fields
    assert "custom_fees" in data["property"]
    assert "preferences" in data["property"]
    assert "settings" in data
    assert "hours" in data["settings"]
    assert "message_templates" in data["settings"]
    assert "preferences" in data["settings"]


# def test_none_property_id() -> None:
#     """Test both endpoints and validation with None property ID"""
#     from src.models.knock_non_admin_property_info import InvalidPropertyIdError
#     from src.tools.property_info import validate_property

#     # Test validate_property directly
#     with pytest.raises(InvalidPropertyIdError) as exc_info:
#         validate_property(None)  # Ignore Linting error
#     assert "Property ID is required" in str(exc_info.value)

#     # Test FastAPI endpoints with missing path parameter
#     response = client.get("/propertyinfo/admin/")
#     assert response.status_code == 404  # FastAPI returns 404 for missing path parameter

#     response = client.get("/propertyinfo/admin/null")
#     assert response.status_code == 422
#     error_detail = response.json()["detail"][0]
#     assert error_detail["msg"] == "Input should be a valid integer, unable to parse string as an integer"
#     assert error_detail["type"] == "int_parsing"
#     assert error_detail["input"] == "null"


def test_mcp_tool_registration() -> None:
    """Test MCP tool registration"""
    from src.tools.property_info import register_property_info_tools

    # Create a mock MCP instance
    mock_mcp = MagicMock()
    mock_tool_decorator = MagicMock()
    mock_mcp.tool = mock_tool_decorator

    # Register the tools
    register_property_info_tools(mock_mcp)

    # Verify tool registration
    assert mock_tool_decorator.call_count == 3  # Should be called twice

    # Get the calls
    calls = mock_tool_decorator.call_args_list

    # Verify admin tool registration (second call)
    admin_call = calls[1]
    assert admin_call.kwargs["name"] == "get_knock_admin_property_info"
    assert "Get the property info of admin knock properties" in admin_call.kwargs["description"]


def test_async_endpoints() -> None:
    """Test the async FastAPI endpoint functions"""
    import asyncio

    from src.tools.property_info import (
        # knock_non_admin_property_info,
        knock_admin_property_info,
    )

    # Test admin endpoint
    property_id = 123
    response = asyncio.run(knock_admin_property_info(property_id))
    assert response.property.id == property_id
    assert response.status_code == "ok"
    assert response.property.data.property_id == property_id
    assert response.property.data.location.name == f"Property {property_id}"
    assert response.settings.preferences.script_major_version == "3"
    assert len(response.property.custom_fees) > 0


# def test_property_info_none_property_id() -> None:
#     """Test the admin endpoint with None property ID passed as query parameter"""
#     from src.models.knock_non_admin_property_info import InvalidPropertyIdError
#     from src.tools.property_info import (
#         # get_knock_non_admin_property_info,
#         get_knock_admin_property_info,
#     )

#     # Test admin endpoint
#     with pytest.raises(InvalidPropertyIdError) as exc_info:
#         get_knock_admin_property_info(None)  # Ignore Linting error
#     assert "Property ID is required" in str(exc_info.value)
