from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
from starlette import status

from src.tools.pna import router

app = FastAPI()
app.include_router(router)

client = TestClient(app)


def test_availability_valid_integer_bedrooms():
    response = client.get(
        "/availability",
        params={"property_id": 1, "bedrooms": 2, "move_date": "2025-07-08"},
    )
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["requested_bedrooms"] == 2
    assert data["availability"] == "matched"


def test_availability_valid_studio_bedrooms():
    response = client.get("/availability", params={"property_id": 1, "bedrooms": "studio"})
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["requested_bedrooms"] == "studio"
    assert data["availability"] == "matched"


def test_availability_valid_bedrooms_string_integer():
    response = client.get("/availability", params={"property_id": 1, "bedrooms": "2"})
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["requested_bedrooms"] == 2
    assert data["availability"] == "matched"


def test_availability_invalid_bedrooms_zero():
    response = client.get("/availability", params={"property_id": 1, "bedrooms": 0})
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Must be 'studio' or a positive integer." in response.text


def test_availability_invalid_bedrooms_float_string():
    response = client.get("/availability", params={"property_id": 1, "bedrooms": "2.5"})
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Must be 'studio' or a positive integer." in response.text


def test_availability_invalid_bedrooms_negative():
    response = client.get("/availability", params={"property_id": 1, "bedrooms": -1})
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Must be 'studio' or a positive integer." in response.text


def test_availability_invalid_bedrooms_string():
    response = client.get("/availability", params={"property_id": 1, "bedrooms": "penthouse"})
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Must be 'studio' or a positive integer." in response.text


def test_availability_invalid_bedrooms_empty_string():
    response = client.get("/availability", params={"property_id": 1, "bedrooms": ""})
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Must be 'studio' or a positive integer." in response.text


def test_availability_missing_bedrooms():
    response = client.get("/availability", params={"property_id": 1})
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Field required" in response.text


def test_availability_missing_property_id():
    response = client.get("/availability")
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


def test_availability_without_move_date():
    response = client.get("/availability", params={"property_id": 1, "bedrooms": "studio"})
    assert response.status_code == status.HTTP_200_OK
