# Stage 1: Build stage
# Use a full Python image to build dependencies
FROM python:3.11-slim AS builder

# Set the working directory
WORKDIR /app

# Install uv
RUN pip install uv

# Copy dependency definition files
COPY pyproject.toml ./

# Install dependencies into a virtual environment
# This caches dependencies in a separate layer
RUN mkdir src &&uv venv && \
    uv sync --all-extras --dev -p python3

# Stage 2: Production stage
# Use a slim image for the final application
FROM python:3.11-slim AS production

# Set the working directory
WORKDIR /app

# Set environment variables for production
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/app/.venv/bin:$PATH"

# Create a non-root user
RUN useradd --create-home appuser
USER appuser

# Copy the virtual environment from the builder stage
COPY --from=builder /app/.venv ./.venv

# Copy the application source code
COPY . .

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
