"""
Pydantic models for non-admin property info response.
"""

from datetime import datetime

from pydantic import BaseModel, Field


class QuickLaunchMenuItem(BaseModel):
    enabled: bool
    name: str


class Doorway(BaseModel):
    agentGuidedTourDisclaimerText: str
    applyNowIsActive: bool
    availabilityIsActive: bool
    bannerText: str
    customAppointmentMessage: str
    dynamicNumberInsertionType: str
    excludeAppointmentMessagePhone: bool
    faqsIsActive: bool
    formattedNumber: str
    formattedNumberIsActive: bool
    galleryIsActive: bool
    hideKnockBranding: bool
    hidePricing: bool
    hideUnitPreferences: bool
    hideUnitSelector: bool
    includeInQuickLaunchMenuList: list[QuickLaunchMenuItem]
    leasingInfoIsActive: bool
    leasingSpecialIsActive: bool
    limitAvailableUnitsShown: bool
    liveVideoTourDisclaimerText: str
    neighborhoodIsActive: bool
    petInfoIsActive: bool
    renterPortalIsActive: bool
    requestTextIsActive: bool
    residentPortalURL: str
    scheduleATourContentBox: str
    selfGuidedTourDisclaimerText: str
    showNoteInput: bool
    somethingElseIsActive: bool
    useCustomWebsite: bool
    useDynamicFloorplans: bool


class KeySellingPoints(BaseModel):
    community: list[str]
    location: list[str]
    units: list[str]


class Application(BaseModel):
    fee: str
    instructions: str
    isDefaultApplicationUrlOverridden: bool
    link: str


class LeaseLength(BaseModel):
    isAvailable: bool
    leaseLength: str
    lengthUnit: str | None = None


class LeasingTerms(BaseModel):
    breakPenalty: str
    deposit: str
    includeUpcharges: bool
    leaseLengths: list[LeaseLength]
    leasingSpecial: str
    notes: str


class Leasing(BaseModel):
    application: Application
    provider: str
    qualificationCriteria: str
    terms: LeasingTerms


class Address(BaseModel):
    city: str
    house: str
    neighborhood: str
    raw: str
    state: str
    street: str
    zip: str


class GeoLocation(BaseModel):
    coordinates: list[float]
    type: str


class Location(BaseModel):
    address: Address
    geo: GeoLocation
    name: str
    needs_review: bool
    numberOfUnits: int
    timezone: str
    yearBuilt: int


class Logo(BaseModel):
    isError: bool
    isLocal: bool
    progress: int
    url: str


class PetPolicy(BaseModel):
    cats: bool
    large_dogs: bool
    none: bool
    small_dogs: bool


class Pets(BaseModel):
    allowed: PetPolicy
    deposit: str
    fee: str
    notes: str
    rent: str


class Social(BaseModel):
    facebook: str
    knock_email: str
    shortlink: str
    website: str


class Amenity(BaseModel):
    available: bool
    name: str


class PropertyData(BaseModel):
    coverPhoto: dict = Field(default_factory=dict)
    customDetails: list = Field(default_factory=list)
    doorway: Doorway
    floorplan: dict = Field(default_factory=dict)
    id: str
    key_selling_points: KeySellingPoints
    leasing: Leasing
    location: Location
    logos: list[Logo]
    notes: str
    pets: Pets
    photos: list = Field(default_factory=list)
    property_id: int
    social: Social


def get_iso_datetime() -> str:
    """Return current datetime in ISO format without microseconds"""
    return datetime.now().replace(microsecond=0).isoformat()


class Property(BaseModel):
    created_time: str = Field(default_factory=get_iso_datetime)
    data: PropertyData
    id: int
    is_deleted: bool
    leasing_team_id: int
    modified_time: str = Field(default_factory=get_iso_datetime)
    owning_manager_id: int
    public_id: str
    resource_id: int
    timezone: str
    type: str


class PropertyResponse(BaseModel):
    property: Property
    status_code: str


class InvalidPropertyIdError(ValueError):
    def __init__(self, value: str):
        super().__init__(f"Property ID value '{value}' is invalid. Must be a positive integer.")
