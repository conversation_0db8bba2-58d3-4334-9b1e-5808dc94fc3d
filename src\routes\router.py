from fastapi import APIRouter

from utils.log_config import get_logger

router = APIRouter()

logger = get_logger(__name__)


@router.get(
    "/health",
    tags=["healthcheck"],
    summary="Perform a Health Check",
    response_description="Return HTTP Status Code 200 (OK)",
    operation_id="healthcheck",
    include_in_schema=False,
)
def health_check() -> dict[str, str]:
    """
    Health check endpoint to verify that the service is up and running.
    """
    return {"status": "ok", "message": "Service is healthy"}
